package com.healthdiet.recipe;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
// import org.springframework.cloud.client.discovery.EnableDiscoveryClient;  // 暂时禁用
// import org.springframework.cloud.openfeign.EnableFeignClients;  // 暂时禁用

/**
 * 菜谱服务启动类
 */
@SpringBootApplication
// @EnableDiscoveryClient  // 暂时禁用服务发现
// @EnableFeignClients  // 暂时禁用Feign客户端
@MapperScan("com.healthdiet.recipe.mapper")
public class RecipeApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(RecipeApplication.class, args);
    }
}
