package com.healthdiet.user;

// import org.mybatis.spring.annotation.MapperScan;  // 暂时禁用
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
// import org.springframework.cloud.client.discovery.EnableDiscoveryClient;  // 暂时禁用
// import org.springframework.cloud.openfeign.EnableFeignClients;  // 暂时禁用

/**
 * 用户服务启动类
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
// @EnableDiscoveryClient  // 暂时禁用服务发现
// @EnableFeignClients  // 暂时禁用Feign客户端
// @MapperScan("com.healthdiet.user.mapper")  // 暂时禁用MyBatis
public class UserApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
    }
}
