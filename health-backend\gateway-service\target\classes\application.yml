server:
  port: 8080

spring:
  application:
    name: gateway-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        enabled: false  # 禁用服务发现
      config:
        server-addr: localhost:8848
        file-extension: yml
        enabled: false  # 禁用配置中心
        import-check:
          enabled: false  # 禁用配置导入检查
    gateway:
      discovery:
        locator:
          enabled: false  # 禁用自动路由发现
          lower-case-service-id: true
      routes:
        # 认证服务路由 - 使用固定地址
        - id: auth-service
          uri: http://localhost:8081  # 直接指向服务地址
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2

        # 用户服务路由 - 使用固定地址
        - id: user-service
          uri: http://localhost:8082  # 直接指向服务地址
          predicates:
            - Path=/api/users/**
          filters:
            - StripPrefix=2

        # 菜谱服务路由 - 使用固定地址
        - id: recipe-service
          uri: http://localhost:8083  # 直接指向服务地址
          predicates:
            - Path=/api/recipes/**
          filters:
            - StripPrefix=2

        # AIGC服务路由 - 使用固定地址
        - id: aigc-service
          uri: http://localhost:8085  # 直接指向服务地址
          predicates:
            - Path=/api/aigc/**
          filters:
            - StripPrefix=2

        # 计划服务路由
        - id: plan-service
          uri: lb://plan-service
          predicates:
            - Path=/api/plans/**
          filters:
            - StripPrefix=2

        # 管理服务路由
        - id: admin-service
          uri: lb://admin-service
          predicates:
            - Path=/api/admin/**
          filters:
            - StripPrefix=2
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true

# Sentinel配置
management:
  endpoints:
    web:
      exposure:
        include: "*"

logging:
  level:
    com.healthdiet.gateway: debug
    org.springframework.cloud.gateway: debug
