server:
  port: 8081

spring:
  application:
    name: auth-service
  # config:
    # 这是唯一需要保留的关于 Nacos Config 的配置 - 暂时禁用
    # import: "optional:nacos:auth-service.yml"
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************
    username: root
    password: mysql123
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # Nacos服务发现配置 - 暂时禁用
  # cloud:
  #   nacos:
  #     discovery:
  #       server-addr: localhost:8848
  #       enabled: false  # 禁用服务发现
  #     config:
  #       import-check:
  #         enabled: false  # 禁用配置导入检查

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.healthdiet.auth: debug
    com.baomidou.mybatisplus: debug

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
