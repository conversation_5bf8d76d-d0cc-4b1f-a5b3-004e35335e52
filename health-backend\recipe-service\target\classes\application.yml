server:
  port: 8083

spring:
  application:
    name: recipe-service
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************
    username: root
    password: mysql123
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # Nacos配置 - 暂时禁用
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        enabled: false  # 禁用服务发现
      config:
        server-addr: localhost:8848
        file-extension: yml
        enabled: false  # 禁用配置中心
        import-check:
          enabled: false  # 禁用配置导入检查

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath:mapper/*.xml

# 日志配置
logging:
  level:
    com.healthdiet.recipe: debug
    com.baomidou.mybatisplus: debug

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
