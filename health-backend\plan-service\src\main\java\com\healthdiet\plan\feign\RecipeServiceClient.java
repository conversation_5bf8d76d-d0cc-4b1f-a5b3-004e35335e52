package com.healthdiet.plan.feign;

// import org.springframework.cloud.openfeign.FeignClient;  // 暂时禁用
// import org.springframework.web.bind.annotation.GetMapping;  // 暂时禁用
// import org.springframework.web.bind.annotation.PathVariable;  // 暂时禁用

/**
 * 菜谱服务Feign客户端 - 暂时禁用Feign功能
 */
public class RecipeServiceClient {

    // 暂时禁用Feign客户端功能
    // 等恢复Nacos服务发现后再启用

    /**
     * 获取菜谱详情的模拟方法
     */
    public RecipeInfo getRecipeById(Long id) {
        // 临时返回模拟数据
        RecipeInfo info = new RecipeInfo();
        info.setId(id);
        info.setTitle("临时菜谱-" + id);
        info.setCoverImageUrl("temp-image.jpg");
        info.setEstimatedCalories(300);
        return info;
    }

    /**
     * 获取菜谱食材的模拟方法
     */
    public java.util.List<RecipeIngredient> getRecipeIngredients(Long id) {
        // 临时返回空列表
        return new java.util.ArrayList<>();
    }

    /**
     * 菜谱信息DTO
     */
    public static class RecipeInfo {
        private Long id;
        private String title;
        private String coverImageUrl;
        private Integer estimatedCalories;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getCoverImageUrl() { return coverImageUrl; }
        public void setCoverImageUrl(String coverImageUrl) { this.coverImageUrl = coverImageUrl; }

        public Integer getEstimatedCalories() { return estimatedCalories; }
        public void setEstimatedCalories(Integer estimatedCalories) { this.estimatedCalories = estimatedCalories; }
    }

    /**
     * 菜谱食材DTO
     */
    public static class RecipeIngredient {
        private String ingredientName;
        private String quantity;

        // Getters and Setters
        public String getIngredientName() { return ingredientName; }
        public void setIngredientName(String ingredientName) { this.ingredientName = ingredientName; }

        public String getQuantity() { return quantity; }
        public void setQuantity(String quantity) { this.quantity = quantity; }
    }

    /**
     * 通用结果包装类
     */
    public static class Result<T> {
        private Integer code;
        private String message;
        private T data;

        // Getters and Setters
        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
    }
}
