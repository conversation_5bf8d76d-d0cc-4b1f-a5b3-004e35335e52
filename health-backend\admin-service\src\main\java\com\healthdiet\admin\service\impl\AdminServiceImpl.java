package com.healthdiet.admin.service.impl;

import com.healthdiet.admin.dto.AdminLoginRequest;
import com.healthdiet.admin.dto.AdminResponse;
import com.healthdiet.admin.dto.DashboardStatsResponse;
import com.healthdiet.admin.entity.Admin;
// import com.healthdiet.admin.feign.RecipeServiceClient;  // 暂时禁用
// import com.healthdiet.admin.feign.UserServiceClient;  // 暂时禁用
import com.healthdiet.admin.mapper.AdminMapper;
import com.healthdiet.admin.service.AdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 管理员服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminServiceImpl implements AdminService {
    
    private final AdminMapper adminMapper;
    // private final UserServiceClient userServiceClient;  // 暂时禁用
    // private final RecipeServiceClient recipeServiceClient;  // 暂时禁用
    
    @Override
    public String login(AdminLoginRequest request) {
        // 根据用户名查询管理员
        Admin admin = adminMapper.selectByUsername(request.getUsername());
        if (admin == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 验证密码（这里简化处理，实际应该使用加密）
        if (!admin.getPassword().equals(request.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 检查状态
        if (admin.getStatus() == 0) {
            throw new RuntimeException("账户已被禁用");
        }
        
        // 生成JWT Token（这里简化处理，返回管理员ID）
        return "admin_token_" + admin.getId();
    }
    
    @Override
    public AdminResponse getAdminInfo(Long adminId) {
        Admin admin = adminMapper.selectById(adminId);
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }
        
        return convertToResponse(admin);
    }
    
    @Override
    public DashboardStatsResponse getDashboardStats() {
        DashboardStatsResponse stats = new DashboardStatsResponse();
        
        try {
            // 获取用户统计数据 - 暂时使用模拟数据
            // UserServiceClient.Result<Long> userCountResult = userServiceClient.getUserCount();
            // if (userCountResult.getCode() == 200) {
            //     stats.setTotalUsers(userCountResult.getData());
            // }
            stats.setTotalUsers(1000L); // 临时模拟数据

            // UserServiceClient.Result<Long> todayUserResult = userServiceClient.getTodayNewUserCount();
            // if (todayUserResult.getCode() == 200) {
            //     stats.setTodayNewUsers(todayUserResult.getData());
            // }
            stats.setTodayNewUsers(50L); // 临时模拟数据
        } catch (Exception e) {
            log.warn("获取用户统计数据失败: {}", e.getMessage());
            stats.setTotalUsers(0L);
            stats.setTodayNewUsers(0L);
        }
        
        try {
            // 获取菜谱统计数据 - 暂时使用模拟数据
            // RecipeServiceClient.Result<Long> recipeCountResult = recipeServiceClient.getRecipeCount();
            // if (recipeCountResult.getCode() == 200) {
            //     stats.setTotalRecipes(recipeCountResult.getData());
            // }
            stats.setTotalRecipes(500L); // 临时模拟数据

            // RecipeServiceClient.Result<Long> todayRecipeResult = recipeServiceClient.getTodayNewRecipeCount();
            // if (todayRecipeResult.getCode() == 200) {
            //     stats.setTodayNewRecipes(todayRecipeResult.getData());
            // }
            stats.setTodayNewRecipes(20L); // 临时模拟数据

            // RecipeServiceClient.Result<Long> aiRecipeResult = recipeServiceClient.getAiRecipeCount();
            // if (aiRecipeResult.getCode() == 200) {
            //     stats.setAiGeneratedRecipes(aiRecipeResult.getData());
            // }
            stats.setAiGeneratedRecipes(200L); // 临时模拟数据

            // RecipeServiceClient.Result<Long> userRecipeResult = recipeServiceClient.getUserRecipeCount();
            // if (userRecipeResult.getCode() == 200) {
            //     stats.setUserCreatedRecipes(userRecipeResult.getData());
            // }
            stats.setUserCreatedRecipes(300L); // 临时模拟数据

            // RecipeServiceClient.Result<Long> favoriteCountResult = recipeServiceClient.getFavoriteCount();
            // if (favoriteCountResult.getCode() == 200) {
            //     stats.setTotalFavorites(favoriteCountResult.getData());
            // }
            stats.setTotalFavorites(1500L); // 临时模拟数据

            // RecipeServiceClient.Result<Long> todayFavoriteResult = recipeServiceClient.getTodayNewFavoriteCount();
            // if (todayFavoriteResult.getCode() == 200) {
            //     stats.setTodayNewFavorites(todayFavoriteResult.getData());
            // }
            stats.setTodayNewFavorites(80L); // 临时模拟数据
        } catch (Exception e) {
            log.warn("获取菜谱统计数据失败: {}", e.getMessage());
            stats.setTotalRecipes(0L);
            stats.setTodayNewRecipes(0L);
            stats.setAiGeneratedRecipes(0L);
            stats.setUserCreatedRecipes(0L);
            stats.setTotalFavorites(0L);
            stats.setTodayNewFavorites(0L);
        }
        
        return stats;
    }
    
    /**
     * 转换为响应DTO
     */
    private AdminResponse convertToResponse(Admin admin) {
        AdminResponse response = new AdminResponse();
        response.setId(admin.getId());
        response.setUsername(admin.getUsername());
        response.setRealName(admin.getRealName());
        response.setEmail(admin.getEmail());
        response.setRole(admin.getRole());
        response.setRoleName(getRoleName(admin.getRole()));
        response.setStatus(admin.getStatus());
        response.setStatusName(getStatusName(admin.getStatus()));
        response.setCreatedAt(admin.getCreatedAt());
        response.setUpdatedAt(admin.getUpdatedAt());
        return response;
    }
    
    /**
     * 获取角色名称
     */
    private String getRoleName(Integer role) {
        return switch (role) {
            case 1 -> "超级管理员";
            case 2 -> "普通管理员";
            default -> "未知";
        };
    }
    
    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        return switch (status) {
            case 1 -> "启用";
            case 0 -> "禁用";
            default -> "未知";
        };
    }
}
